import { styled } from '@superset-ui/core';
import Icons from 'src/components/Icons';
import { BusinessDomain, FolderData } from '../types';

interface BreadcrumbNavigationProps {
  currentBusiness?: BusinessDomain;
  selectedFolder: string;
  currentFolders: FolderData[];
  onBreadcrumbClick: (folderId?: string) => void;
}

const BreadcrumbContainer = styled.div`
  padding: ${({ theme }) => theme.gridUnit * 3}px
    ${({ theme }) => theme.gridUnit * 6}px;
  font-size: ${({ theme }) => theme.typography.sizes.s}px;
`;

const BreadcrumbContent = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.gridUnit * 1.5}px;
`;

const BreadcrumbWrapper = styled.div`
  display: flex;
  align-items: center;
`;

const BreadcrumbLink = styled.span<{
  businessColor?: string;
  isActive?: boolean;
  isClickable?: boolean;
  uppercase?: boolean;
}>`
  color: ${({ businessColor, isActive, theme }) =>
    isActive ? theme.colors.grayscale.dark1 : businessColor};
  cursor: ${({ isClickable }) => (isClickable ? 'pointer' : 'default')};
  font-weight: ${({ isActive }) => (isActive ? 600 : 400)};
  text-decoration: none;
  text-transform: ${({ uppercase }) => (uppercase ? 'uppercase' : 'none')};
  transition: text-decoration 0.2s ease;

  &:hover {
    text-decoration: ${({ isClickable }) =>
      isClickable ? 'underline' : 'none'};
  }
`;

const BreadcrumbSeparator = styled(Icons.RightOutlined)`
  margin-right: ${({ theme }) => theme.gridUnit * 1.5}px;
`;

const buildBreadcrumbPath = (
  folderId: string,
  folders: FolderData[],
): FolderData[] => {
  for (const folder of folders) {
    if (folder.id === folderId) {
      return [folder];
    }
    if (folder.subfolders) {
      const subPath = buildBreadcrumbPath(folderId, folder.subfolders);
      if (subPath.length > 0) {
        return [folder, ...subPath];
      }
    }
  }
  return [];
};

const BreadcrumbNavigation = ({
  currentBusiness,
  selectedFolder,
  currentFolders,
  onBreadcrumbClick,
}: BreadcrumbNavigationProps) => {
  const buildPath = () => {
    const breadcrumbPath = buildBreadcrumbPath(selectedFolder, currentFolders);
    return breadcrumbPath.map((folder, index) => {
      const isLast = index === breadcrumbPath.length - 1;
      return (
        <BreadcrumbWrapper key={folder.id}>
          <BreadcrumbSeparator iconSize="xs" />
          <BreadcrumbLink
            isActive={isLast}
            isClickable={!isLast}
            onClick={() => (!isLast ? onBreadcrumbClick(folder.id) : undefined)}
          >
            {folder.title}
          </BreadcrumbLink>
        </BreadcrumbWrapper>
      );
    });
  };

  return (
    <BreadcrumbContainer>
      <BreadcrumbContent>
        <Icons.HomeOutlined iconSize="xs" iconColor={currentBusiness?.color} />
        <BreadcrumbLink
          businessColor={currentBusiness?.color}
          isActive={!selectedFolder}
          onClick={() => onBreadcrumbClick()}
          isClickable
          uppercase
        >
          {currentBusiness?.name}
        </BreadcrumbLink>

        {selectedFolder && buildPath()}
      </BreadcrumbContent>
    </BreadcrumbContainer>
  );
};

export default BreadcrumbNavigation;
