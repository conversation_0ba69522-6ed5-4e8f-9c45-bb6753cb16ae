import { useEffect, useRef, useState } from 'react';
import { useParams, useHistory } from 'react-router-dom';
import { isEqual } from 'lodash';
import { styled, SupersetClient, t } from '@superset-ui/core';
import { bootstrapData } from 'src/preamble';
import SubMenu from 'src/features/home/<USER>';
import { useToasts } from 'src/components/MessageToasts/withToasts';
import Loading from 'src/components/Loading';
import { Radio } from 'src/components/Radio';
import { RadioChangeEvent } from 'src/components';
import FolderCard from '../../components/FolderCard';
import {
  Dashboard,
  Entity,
  EntityType,
  Folder,
  FolderListDashboard,
  FolderSlug,
} from '../../types';
import FolderDashboardList from '../../components/FolderDashboardList';
import { reduceContentMap } from '../../utils';

const locale = bootstrapData?.common?.locale || 'en';

const StyledContainer = styled.div`
  padding-inline: ${({ theme }) => theme.gridUnit * 4}px;
  padding-bottom: ${({ theme }) => theme.gridUnit * 4}px;
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: ${({ theme }) => theme.gridUnit * 4}px;
  flex: 1;

  .folder-dashboard-list-view {
    margin: 0 !important;
    border: 1px solid ${({ theme }) => theme.colors.grayscale.light3};
    border-radius: 0 0 ${({ theme }) => theme.gridUnit}px
      ${({ theme }) => theme.gridUnit}px !important;

    .header {
      padding: ${({ theme }) => theme.gridUnit * 4}px;
      background-color: ${({ theme }) => theme.colors.grayscale.light5};
    }

    .table {
      margin: 0;
    }
  }
`;

const RadioGroup = styled(Radio.Group)`
  display: flex;
  align-items: center;
`;

const FolderPage = () => {
  const { slug } = useParams<{ slug: string }>();
  const [folderData, setFolderData] = useState<Folder | null>(null);
  const [content, setContent] = useState<Entity[]>([]);
  const [loadingFolder, setLoadingFolder] = useState(false);
  const [saving, setSaving] = useState(false);
  const [selectedFolderData, setSelectedFolderData] = useState<{
    id: number;
    dashboards: Set<number>;
  } | null>(null);
  const initialContentMap = useRef<Record<number, Entity[]>>({});
  const currentContentMap = useRef<Record<number, Entity[]>>({});
  const { addSuccessToast, addDangerToast } = useToasts();
  const history = useHistory();

  const updateInitialContentMap = (
    folderId: number,
    action: 'add' | 'remove' | 'update',
    entities: Entity[],
  ) => {
    initialContentMap.current = reduceContentMap(
      initialContentMap.current,
      folderId,
      action,
      entities,
    );
  };
  const updateCurrentContentMap = (
    folderId: number,
    action: 'add' | 'remove' | 'update',
    entities: Entity[],
  ) => {
    currentContentMap.current = reduceContentMap(
      currentContentMap.current,
      folderId,
      action,
      entities,
    );
  };

  const canSave =
    !isEqual(initialContentMap.current, currentContentMap.current) && !saving;

  useEffect(() => {
    const fetchFolderData = async () => {
      setLoadingFolder(true);
      try {
        const response = await SupersetClient.get({
          endpoint: `/api/v1/folder/${slug}`,
        });
        const { folder } = response.json;
        setFolderData(folder);
        setContent(folder.children);

        const createContentMap = (folder: Entity) => {
          const contentMap: Record<number, Entity[]> = {};
          const processEntity = (entity: Entity) => {
            if (entity.item_type === EntityType.Folder) {
              contentMap[entity.id] = entity.children || [];
              if (entity.children) {
                entity.children.forEach(processEntity);
              }
            }
          };
          processEntity(folder);
          return contentMap;
        };
        const contentMap = createContentMap(folder);
        initialContentMap.current = contentMap;
        currentContentMap.current = contentMap;
      } catch (error) {
        addDangerToast(t('Failed to fetch folder data'));
      } finally {
        setLoadingFolder(false);
      }
    };
    fetchFolderData();
  }, [addDangerToast, slug]);

  const addEntitiesToData = (
    currentData: Entity[],
    newEntities: Entity[],
  ): Entity[] =>
    currentData.map(entity => {
      if (
        entity.id === selectedFolderData?.id &&
        entity.item_type === EntityType.Folder
      ) {
        const updatedFolder = {
          ...entity,
          children: [...(entity.children || []), ...newEntities],
        };

        updateCurrentContentMap(
          updatedFolder.id,
          'update',
          updatedFolder.children,
        );

        return updatedFolder;
      }
      if (entity.item_type === EntityType.Folder && entity.children) {
        return {
          ...entity,
          children: addEntitiesToData(entity.children, newEntities),
        };
      }
      return entity;
    });

  const addDashboardsToFolder = (dashboards: FolderListDashboard[]) => {
    const dashboardEntities: Dashboard[] = dashboards
      .filter(entity => !selectedFolderData!.dashboards.has(entity.id))
      .map(dashboard => ({
        id: dashboard.id,
        name_ru: dashboard.dashboard_title_ru,
        name_en: dashboard.dashboard_title,
        parent_id: selectedFolderData!.id,
        item_type: EntityType.Dashboard,
        certified_by: dashboard.certified_by,

        // only for typisation
        owners: [],
        changed_on_delta_humanized: '',
        published: true,
        tags: [],
      }));
    setContent(prevData => {
      const newData = addEntitiesToData(prevData, dashboardEntities);

      setSelectedFolderData({
        id: selectedFolderData!.id,
        dashboards: new Set([
          ...selectedFolderData!.dashboards,
          ...dashboardEntities.map(entity => entity.id),
        ]),
      });
      return newData;
    });
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      const updateFolderContent = (value: {
        id: number;
        children: Entity[];
      }) => {
        const payload = value.children.map(child => ({
          type: child.item_type,
          id: child.id,
        }));
        return SupersetClient.put({
          endpoint: `/api/v1/folder/${value.id}/content`,
          jsonPayload: payload,
        });
      };

      const changedFolders = Object.entries(currentContentMap.current).reduce(
        (acc: { id: number; children: Entity[] }[], entry) => {
          const folderId = Number(entry[0]);
          const children = entry[1];
          if (!isEqual(children, initialContentMap.current[folderId])) {
            acc.push({ id: folderId, children: entry[1] });
          }
          return acc;
        },
        [],
      );

      // Only send requests for changed folders
      if (changedFolders.length > 0) {
        await Promise.all(changedFolders.map(updateFolderContent));

        // After successful save, update initialContentMap to match currentContentMap
        initialContentMap.current = currentContentMap.current;

        addSuccessToast(
          t(
            'Changes saved successfully for %s folder(s).',
            changedFolders.length,
          ),
        );
      } else {
        addSuccessToast(t('No changes to save.'));
      }
    } catch (error) {
      addDangerToast(t('Failed to save changes.'));
    } finally {
      setSaving(false);
    }
  };

  const handleChangeSlug = (event: RadioChangeEvent) => {
    history.push(`/folder/${event.target.value}`);
  };

  return (
    <>
      <SubMenu
        name={t('Edit folder')}
        extraNodeAfterName={
          <RadioGroup value={slug} onChange={handleChangeSlug}>
            <Radio.Button value={FolderSlug.Global}>Standalone</Radio.Button>
            <Radio.Button value={FolderSlug.Plugin}>Plugin</Radio.Button>
          </RadioGroup>
        }
      />
      <StyledContainer>
        <FolderCard
          title={
            locale === 'ru'
              ? folderData?.name_ru || folderData?.name_en || ''
              : folderData?.name_en || folderData?.name_ru || ''
          }
          slug={folderData?.slug || null}
          rootFolderId={folderData?.id || 0}
          editMode
          canSave={canSave}
          loading={loadingFolder}
          content={content}
          setContent={setContent}
          setSelectedFolderData={setSelectedFolderData}
          selectedfolderId={selectedFolderData?.id}
          currentContentMap={currentContentMap.current}
          updateInitialContentMap={updateInitialContentMap}
          updateCurrentContentMap={updateCurrentContentMap}
          handleSave={handleSave}
        />
        <FolderDashboardList
          addDashboardsToFolder={addDashboardsToFolder}
          selectedFolderData={selectedFolderData}
        />
        {saving && <Loading />}
      </StyledContainer>
    </>
  );
};

export default FolderPage;
