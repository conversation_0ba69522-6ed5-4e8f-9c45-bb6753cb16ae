import logging

from flask import request, Response
from flask_appbuilder.api import expose, protect, safe

from superset.extensions import databricks_client
from superset.views.base_api import BaseSupersetApi

logger = logging.getLogger(__name__)


class DataSearchRestApi(BaseSupersetApi):
    """API for AI-powered data search using external Azure Databricks endpoint"""

    resource_name = "search"
    allow_browser_login = True

    @expose("/", methods=("POST",))
    @protect()
    @safe
    def search(self) -> Response:
        """AI-powered dashboards search"""
        try:
            data = request.get_json()

            query = data.get("query", "")
            top_k = data.get("top_k", 3)

            if not query or len(query.strip()) < 3:
                return self.response_400(
                    message="query must be at least 3 characters long"
                )

            if not isinstance(top_k, int) or 0 > top_k > 10:
                return self.response_400(
                    message="top_k must be an integer between 1 and 10"
                )

            response = databricks_client.ai_search(query, top_k)

            if response.status_code != 200:
                logger.error(
                    "Databricks AI search returned status %s: %s",
                    response.status_code,
                    response.text,
                )
                return self.response_400(message="External search service unavailable")

            result = response.json()
            return self.response(200, result=result)

        except Exception as ex:  # pylint: disable=broad-exception-caught
            logger.exception("Unexpected error in search API: %s", str(ex))
            return self.response_500(message="Internal server error")
