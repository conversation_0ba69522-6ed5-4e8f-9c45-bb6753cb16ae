import { ErrorLevel, SupersetClient, t } from '@superset-ui/core';
import { useState, useRef, useEffect, useCallback } from 'react';
import Loading from 'src/components/Loading';
import BasicErrorAlert from 'src/components/ErrorMessage/BasicErrorAlert';
import { EmptyStateSmall } from 'src/components/EmptyState';
import {
  SearchLoader,
  ResultsContainer,
  ResultsHeader,
  QueryInfo,
  ResultsList,
  ResultItem,
  ResultContent,
  ResultTitle,
  ResultMeta,
  ScoreContainer,
  ScoreValue,
  ScoreBar,
  ScoreLabel,
  ErrorAlertWrapper,
  EmptyStateWrapper,
  SearchClose,
  SearchUnderLayer,
  SearchWrapper,
  SearchInput,
} from './styles';

interface Response {
  predictions: {
    dashboards: {
      title: string;
      score: number;
      url: string;
    }[];
    meta: {
      query: string;
      retrieved: number;
      top_k: number;
    };
  };
}

const EnhancedDataSearch = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [response, setResponse] = useState<Response | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<{
    level: ErrorLevel;
    title: string;
    body: string;
  } | null>(null);
  const [showAllResults, setShowAllResults] = useState(false);

  const inputRef = useRef<any>(null);
  const searchUnderLayerRef = useRef<HTMLDivElement>(null);
  const [position, setPosition] = useState<{
    right: number;
    top: number;
  } | null>(null);

  const handleOpen = () => {
    setIsOpen(true);
  };

  const handleClose = useCallback(() => {
    setIsOpen(false);
    setSearchQuery('');
    setResponse(null);
    setError(null);
    setShowAllResults(false);
  }, []);

  useEffect(() => {
    const calculateRightPosition = () => {
      if (searchUnderLayerRef.current) {
        const { offsetLeft, offsetTop, clientWidth } =
          searchUnderLayerRef.current;
        const newRight = window.innerWidth - offsetLeft - clientWidth;
        setPosition({ right: newRight, top: offsetTop });
      }
    };
    setTimeout(calculateRightPosition, 300);

    const handleResize = () => calculateRightPosition();

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';

      if (inputRef.current) {
        inputRef.current.focus();
      }
    } else {
      document.body.style.overflow = 'auto';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'auto';
    };
  }, [isOpen, handleClose]);

  const handleSearch = useCallback(async () => {
    if (
      !searchQuery ||
      searchQuery.length < 3 ||
      searchQuery === response?.predictions.meta.query
    )
      return;

    setLoading(true);
    setResponse(null);
    setError(null);

    SupersetClient.post({
      endpoint: '/api/v1/search',
      jsonPayload: {
        query: searchQuery,
        top_k: 10,
      },
    })
      .then(response => {
        setResponse(response.json.result as Response);
      })
      .catch(err => {
        const title =
          err.status >= 500
            ? t('Service temporarily unavailable.')
            : t('Unexpected error. Status: %s', err.status);

        setError({
          level: err.status >= 500 ? 'error' : 'warning',
          title,
          body: err.statusText,
        });
      })
      .finally(() => setLoading(false));
  }, [response?.predictions.meta.query, searchQuery]);

  // Auto-search with debounce
  useEffect(() => {
    if (searchQuery.length >= 3) {
      const timeoutId = setTimeout(handleSearch, 1000);
      return () => clearTimeout(timeoutId);
    }
    return undefined;
  }, [searchQuery, handleSearch]);

  const dashboards = showAllResults
    ? response?.predictions.dashboards
    : response?.predictions.dashboards.slice(0, 3);

  return (
    <>
      <SearchUnderLayer ref={searchUnderLayerRef} />
      {position && (
        <SearchWrapper
          className={isOpen ? 'visible' : ''}
          style={{
            right: position.right,
            top: position.top,
          }}
          onClick={isOpen ? undefined : handleOpen}
        >
          <h4>{t('Find dashboard with AI')}</h4>
          <SearchInput
            ref={inputRef}
            type="text"
            placeholder={t('Find dashboard with AI')}
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
          />

          {isOpen && (
            <>
              {loading && (
                <SearchLoader>
                  <Loading position="inline-centered" />
                  <p>{t('Searching...')}</p>
                </SearchLoader>
              )}

              {error && (
                <ErrorAlertWrapper>
                  <BasicErrorAlert
                    title={error.title}
                    level={error.level}
                    body={error.body}
                  />
                </ErrorAlertWrapper>
              )}

              {response && (
                <ResultsContainer>
                  <ResultsHeader>
                    <p>{t('Search Results')}</p>
                    <QueryInfo>
                      <span
                        role="button"
                        tabIndex={0}
                        onClick={() => setShowAllResults(prev => !prev)}
                      >
                        {showAllResults ? t('Show less') : t('Show more')}
                      </span>
                      <span>
                        {t('Query')}: {response.predictions.meta.query}
                      </span>
                      <span>
                        {t('Found')}: {response.predictions.meta.retrieved}
                      </span>
                    </QueryInfo>
                  </ResultsHeader>

                  <ResultsList>
                    {dashboards?.map(dashboard => (
                      <ResultItem
                        key={dashboard.url}
                        to={dashboard.url}
                        target="_blank"
                        onClick={() => handleClose()}
                      >
                        <ResultContent>
                          <ResultTitle>{dashboard.title}</ResultTitle>
                          <ResultMeta>
                            <span>{t('Dashboard')}</span>
                            <span>•</span>
                            <span>{t('Click to open')}</span>
                          </ResultMeta>
                        </ResultContent>

                        <ScoreContainer>
                          <ScoreValue value={dashboard.score}>
                            {(dashboard.score * 100).toFixed(0)}%
                          </ScoreValue>
                          <ScoreBar score={dashboard.score} />
                          <ScoreLabel>{t('Relevance')}</ScoreLabel>
                        </ScoreContainer>
                      </ResultItem>
                    ))}
                  </ResultsList>
                </ResultsContainer>
              )}

              {response?.predictions.dashboards.length === 0 &&
                !error &&
                !loading && (
                  <EmptyStateWrapper>
                    <EmptyStateSmall
                      title={t('No results found')}
                      description={t('Try adjusting your search terms')}
                    />
                  </EmptyStateWrapper>
                )}

              {!response && !error && !loading && (
                <EmptyStateWrapper>
                  <EmptyStateSmall
                    title={t('Type at least 3 characters to search')}
                    image="empty.svg"
                  />
                </EmptyStateWrapper>
              )}
            </>
          )}

          <SearchClose onClick={handleClose} isOpen={isOpen} />
        </SearchWrapper>
      )}
    </>
  );
};

export default EnhancedDataSearch;
