import { useCallback, useEffect, useState } from 'react';
import { API_HANDLER, SupersetClient } from '@superset-ui/core';
import { ResourceStatus } from 'src/hooks/apiResources/apiResources';
import { Folder, FolderSlug } from '../types';

const isStandalone = process.env.type === undefined;

interface FolderResource<T> {
  result: T | null;
  status: ResourceStatus;
  error: Error | null;
  refetch: () => Promise<void>;
}

export const useFolder = (
  slug: FolderSlug,
  canFetch = true,
): FolderResource<Folder> => {
  const [folder, setFolder] = useState<Folder | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchFolder = useCallback(async () => {
    if (!canFetch) {
      return;
    }
    setLoading(true);
    setError(null);
    try {
      const request = async () => {
        if (isStandalone) {
          return SupersetClient.get({
            endpoint: `/api/v1/folder/${slug}`,
          });
        }
        return API_HANDLER.SupersetClient({
          method: 'get',
          url: `/api/v1/folder/${slug}`,
        });
      };
      const response = await request().then(r => (isStandalone ? r.json : r));
      const { folder } = response;
      setFolder(folder);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  }, [slug, canFetch]);

  useEffect(() => {
    fetchFolder();
  }, [fetchFolder]);

  if (loading) {
    return {
      result: null,
      status: ResourceStatus.Loading,
      error: null,
      refetch: fetchFolder,
    };
  }

  if (error) {
    return {
      result: null,
      status: ResourceStatus.Error,
      error,
      refetch: fetchFolder,
    };
  }

  return {
    result: folder as Folder,
    status: ResourceStatus.Complete,
    error: null,
    refetch: fetchFolder,
  };
};
