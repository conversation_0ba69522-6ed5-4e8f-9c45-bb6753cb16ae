import { useCallback } from 'react';
import { TreeProps } from 'antd-v5';
import { t } from '@superset-ui/core';
import {
  Entity,
  EntityType,
  Folder,
  FolderLevel,
  TreeNodeData,
} from '../types';
import { findFolderById } from '../utils';

const insertAfter = (
  children: Entity[],
  entity: Entity,
  afterSiblingKey: string | null,
): Entity[] => {
  if (!afterSiblingKey) {
    return [...children, entity];
  }
  const [siblingType, siblingId] = afterSiblingKey.split('-');
  const index = children.findIndex(
    child =>
      child.item_type === Number(siblingType) && child.id === Number(siblingId),
  );
  // Вставляем после найденного элемента
  return [
    ...children.slice(0, index + 1),
    entity,
    ...children.slice(index + 1),
  ];
};

export const useDragAndDrop = (
  data: Entity[],
  rootFolderId: number,
  onError: (message: string) => void,
  onDataChange: (newData: Entity[]) => void,
  updateCurrentContentMap: (
    folderId: number,
    action: 'add' | 'remove' | 'update',
    entities: Entity[],
  ) => void,
  updateSelectedFolderData: (folder: Folder) => void,
) => {
  const removeEntityByKey = useCallback(
    (entities: Entity[], key: string, parentId): Entity[] => {
      const [entityType, entityId, parentFolderId] = key.split('-').map(Number);

      return entities
        .filter(entity => {
          if (
            entity.item_type === entityType &&
            entity.id === entityId &&
            entity.parent_id === parentFolderId
          ) {
            updateCurrentContentMap(parentId, 'remove', [entity]);
            return false;
          }
          return true;
        })
        .map(entity => {
          if (entity.item_type === EntityType.Folder && entity.children) {
            return {
              ...entity,
              children: removeEntityByKey(entity.children, key, entity.id),
            };
          }
          return entity;
        });
    },
    [updateCurrentContentMap],
  );

  const addEntityToParent = useCallback(
    (
      entities: Entity[],
      entity: Entity,
      parentId: number | null,
      afterSiblingKey: string | null,
    ): Entity[] => {
      if (parentId === rootFolderId) {
        // Add to root level
        const newEntities = insertAfter(entities, entity, afterSiblingKey);
        updateCurrentContentMap(parentId, 'update', newEntities);
        return newEntities;
      }

      return entities.map(item => {
        if (item.id === parentId && item.item_type === EntityType.Folder) {
          const newChildren = insertAfter(
            item.children || [],
            entity,
            afterSiblingKey,
          );
          updateCurrentContentMap(parentId, 'update', newChildren);
          return {
            ...item,
            children: newChildren,
          };
        }
        if (item.item_type === EntityType.Folder && item.children) {
          return {
            ...item,
            children: addEntityToParent(
              item.children,
              entity,
              parentId,
              afterSiblingKey,
            ),
          };
        }
        return item;
      });
    },
    [rootFolderId, updateCurrentContentMap],
  );

  const onDrop: TreeProps['onDrop'] = useCallback(
    async (info: any) => {
      const { dragNode, node, dropToGap } = info;

      try {
        let targetFolderId: number | null = null; // Determine target folder
        let afterSiblingKey: string | null = null; // Determine position

        if (!dropToGap) {
          // Dropped on a node

          if (node.item_type === EntityType.Dashboard) {
            return; // Can't drop on a dashboard, ignore
          }

          targetFolderId = node.id;
          afterSiblingKey = node.key;
        } else {
          // Dropped between nodes

          const { entity, key } = node;
          targetFolderId = entity.parent_id;
          afterSiblingKey = key;
        }

        // Update local state
        const { entity: draggedEntity, key: draggedKey } = dragNode;
        if (draggedEntity) {
          // Remove from current position
          let newData = removeEntityByKey(
            data,
            draggedKey,
            draggedEntity.parent_id,
          );

          // Update parent reference
          const updatedEntity = {
            ...draggedEntity,
            parent_id: targetFolderId || 0,
          };

          // Add to new position
          newData = addEntityToParent(
            newData,
            updatedEntity,
            targetFolderId,
            afterSiblingKey,
          );

          onDataChange(newData);
          updateSelectedFolderData(updatedEntity);
        }
      } catch (error) {
        onError(t('Failed to move item'));
      }
    },
    [
      removeEntityByKey,
      data,
      addEntityToParent,
      onDataChange,
      updateSelectedFolderData,
      onError,
    ],
  );

  const allowDrop: TreeProps['allowDrop'] = useCallback(
    ({ dragNode, dropNode, dropPosition }) => {
      const dragNodeData = dragNode as TreeNodeData;
      const dropNodeData = dropNode as TreeNodeData;

      // Don't allow dropping on dashboards
      if (
        dropNodeData.item_type === EntityType.Dashboard &&
        dropPosition === 0
      ) {
        return false;
      }

      // Don't allow dropping a folder into itself or its descendants
      if (
        dragNodeData.item_type === EntityType.Folder &&
        dropNodeData.item_type === EntityType.Folder
      ) {
        const isDescendant = (parentKey: string, childKey: string): boolean =>
          childKey.startsWith(`${parentKey}-`);

        if (
          dropNodeData.key === dragNodeData.key ||
          isDescendant(dragNodeData.key, dropNodeData.key)
        ) {
          return false;
        }
      }

      // Dont't allow dropping folder into folder with higher level when dropNodeData is folder
      if (
        dragNodeData.entity.item_type === EntityType.Folder &&
        dropNodeData.entity.item_type === EntityType.Folder
      ) {
        if (dropNodeData.entity.level < dragNodeData.entity.level) {
          return false;
        }
      }

      // Dont't allow dropping folder into folder with higher level when dropNodeData is dashboard
      if (
        dragNodeData.entity.item_type === EntityType.Folder &&
        dropNodeData.entity.item_type === EntityType.Dashboard
      ) {
        const parentId = dropNodeData.entity.parent_id;
        if (!parentId) {
          return false;
        }

        const parentFolder = findFolderById(data, parentId);
        if (!parentFolder) {
          return false;
        }

        if (parentFolder.level < dragNodeData.entity.level) {
          return false;
        }
      }

      // Don't allow dropping dashboard into folder where it already exists when dropNodeData is folder
      if (
        dragNodeData.item_type === EntityType.Dashboard &&
        dropNodeData.item_type === EntityType.Folder
      ) {
        if (
          dropNodeData.children?.some(
            child =>
              child.id === dragNodeData.id &&
              child.item_type === dragNodeData.item_type &&
              child.entity.parent_id !== dragNodeData.entity.parent_id,
          )
        ) {
          return false;
        }
      }

      // Don't allow dropping dashboard into folder where it already exists when dropNodeData is dashboard
      if (
        dragNodeData.item_type === EntityType.Dashboard &&
        dropNodeData.item_type === EntityType.Dashboard
      ) {
        const parentId = dropNodeData.entity.parent_id;
        if (!parentId) {
          return false;
        }

        const parentFolder = findFolderById(data, parentId);
        if (!parentFolder) {
          return false;
        }

        if (
          parentFolder.children?.some(
            child =>
              child.id === dragNodeData.id &&
              child.item_type === dragNodeData.item_type &&
              child.parent_id !== dragNodeData.entity.parent_id,
          )
        ) {
          return false;
        }
      }

      // Don't allow dropping a dashboard into a folder except with User level when dropNodeData is folder
      if (
        dragNodeData.item_type === EntityType.Dashboard &&
        dropNodeData.entity.item_type === EntityType.Folder
      ) {
        if (dropNodeData.entity.level !== FolderLevel.User) {
          return false;
        }
      }

      return true;
    },
    [data],
  );

  return {
    onDrop,
    allowDrop,
  };
};
