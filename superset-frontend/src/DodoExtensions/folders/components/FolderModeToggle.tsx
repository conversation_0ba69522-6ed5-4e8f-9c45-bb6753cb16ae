import cx from 'classnames';
import { styled } from '@superset-ui/core';
import Icons from 'src/components/Icons';

const ViewModeContainer = styled.div`
  padding-right: ${({ theme }) => theme.gridUnit * 4}px;
  white-space: nowrap;
  display: flex;
  align-items: center;

  .toggle-button {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 32px;
    height: 32px;
    border-radius: ${({ theme }) => theme.gridUnit}px;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: ${({ theme }) => theme.colors.primary.light4};
    }
  }

  .anticon {
    line-height: 0;
  }

  .active {
    svg {
      color: ${({ theme }) => theme.colors.primary.base};
    }
  }
`;

const FolderModeToggle = ({
  isFolderMode,
  setFolderMode,
}: {
  isFolderMode: boolean;
  setFolderMode: (value: boolean) => void;
}) => (
  <ViewModeContainer>
    <div
      role="button"
      tabIndex={0}
      onClick={e => {
        e.currentTarget.blur();
        setFolderMode(true);
      }}
      className={cx('toggle-button', { active: isFolderMode })}
    >
      <Icons.FolderOpenOutlined iconSize="xl" />
    </div>
    <div
      role="button"
      tabIndex={0}
      onClick={e => {
        e.currentTarget.blur();
        setFolderMode(false);
      }}
      className={cx('toggle-button', {
        active: !isFolderMode,
      })}
    >
      <Icons.ListView iconSize="xl" />
    </div>
  </ViewModeContainer>
);

export default FolderModeToggle;
