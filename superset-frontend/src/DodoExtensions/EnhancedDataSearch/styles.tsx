import { Link } from 'react-router-dom';
import { styled } from '@superset-ui/core';

export const SearchClose = styled.button<{ isOpen: boolean }>`
  position: absolute;
  top: ${({ theme }) => theme.gridUnit * 10}px;
  right: ${({ theme }) => theme.gridUnit * 10}px;
  width: 30px;
  height: 30px;
  cursor: pointer;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  background: none;
  border: none;
  padding: 0;

  ${({ isOpen }) =>
    isOpen &&
    `
      opacity: 1;
      visibility: visible;
      transition-delay: 0.5s;
    `}

  &::before,
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 2px;
    background: ${({ theme }) => theme.colors.grayscale.dark1};
    transform: translate(-50%, -50%) rotate(45deg);
    transition: background 0.3s ease;
  }

  &::after {
    transform: translate(-50%, -50%) rotate(-45deg);
  }

  &:hover {
    &::before,
    &::after {
      background: ${({ theme }) => theme.colors.primary.base};
    }
  }
`;

export const SearchUnderLayer = styled.div`
  margin-right: ${({ theme }) => theme.gridUnit * 2}px;
  width: 180px;
  height: 30px;
`;

export const SearchWrapper = styled.div`
  position: absolute;
  padding: 0 ${({ theme }) => theme.gridUnit * 2}px;
  display: flex;
  align-items: center;
  width: 180px;
  height: 30px;
  background: ${({ theme }) => theme.colors.grayscale.light5};
  border: 1px solid ${({ theme }) => theme.colors.grayscale.light2};
  border-radius: ${({ theme }) => theme.gridUnit * 2}px;
  font-size: ${({ theme }) => theme.typography.sizes.s}px;
  transition:
    all 0.5s ease,
    border 0.3s ease;
  z-index: 1000;
  cursor: pointer;

  &.visible {
    display: block;
    top: 0 !important;
    right: 0 !important;
    width: 100%;
    height: 100vh;
    padding: ${({ theme }) => theme.gridUnit * 10}px;
    border: none;
    border-radius: 0;
    font-size: ${({ theme }) => theme.typography.sizes.xl}px;
    cursor: default;
  }

  h4 {
    margin: 0;
    font-size: ${({ theme }) => theme.typography.sizes.s}px;
    font-weight: ${({ theme }) => theme.typography.weights.normal};
    color: ${({ theme }) => theme.colors.grayscale.base};
    pointer-events: none;
  }

  &.visible h4 {
    display: none;
  }
`;

export const SearchInput = styled.input`
  display: none;
  width: 100%;
  background: transparent;
  border: none;
  outline: none;
  font-size: ${({ theme }) => theme.typography.sizes.xl}px;
  color: ${({ theme }) => theme.colors.grayscale.dark2};
  margin-bottom: ${({ theme }) => theme.gridUnit * 4}px;

  &::placeholder {
    color: ${({ theme }) => theme.colors.grayscale.base};
  }

  .visible & {
    display: block;
  }
`;

export const ErrorAlertWrapper = styled.div`
  margin-top: ${({ theme }) => theme.gridUnit * 4}px;
`;

export const SearchLoader = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.gridUnit * 8}px;
  gap: ${({ theme }) => theme.gridUnit}px;

  p {
    margin: 0;
    color: ${({ theme }) => theme.colors.grayscale.base};
    font-size: ${({ theme }) => theme.typography.sizes.s}px;
  }
`;

export const ResultsContainer = styled.div`
  margin-top: ${({ theme }) => theme.gridUnit * 4}px;
  display: flex;
  flex-direction: column;
  height: 100%;
`;

export const ResultsHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${({ theme }) => theme.gridUnit * 4}px;
  padding-bottom: ${({ theme }) => theme.gridUnit * 2}px;
  border-bottom: 1px solid ${({ theme }) => theme.colors.grayscale.light2};

  p {
    margin: 0;
    font-size: ${({ theme }) => theme.typography.sizes.l}px;
    font-weight: ${({ theme }) => theme.typography.weights.medium};
    color: ${({ theme }) => theme.colors.grayscale.dark2};
  }
`;

export const QueryInfo = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.gridUnit * 2}px;
  font-size: ${({ theme }) => theme.typography.sizes.s}px;
  color: ${({ theme }) => theme.colors.grayscale.base};

  span {
    padding: ${({ theme }) => theme.gridUnit / 2}px
      ${({ theme }) => theme.gridUnit}px;
    background-color: ${({ theme }) => theme.colors.grayscale.light4};
    border-radius: ${({ theme }) => theme.gridUnit / 2}px;
  }

  span:first-of-type {
    background-color: transparent;
    color: ${({ theme }) => theme.colors.primary.base};
    font-weight: ${({ theme }) => theme.typography.weights.medium};
  }
`;

export const ResultsList = styled.div`
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.gridUnit * 3}px;
  flex: 1;
  max-height: calc(100% - 100px);
`;

export const ResultItem = styled(Link)`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${({ theme }) => theme.gridUnit * 4}px;
  border: 1px solid ${({ theme }) => theme.colors.grayscale.light2};
  border-radius: ${({ theme }) => theme.gridUnit * 2}px;
  text-decoration: none;
  transition: all 0.2s ease;

  &:hover {
    border-color: ${({ theme }) => theme.colors.primary.light2};
    box-shadow: 0 2px 8px ${({ theme }) => theme.colors.grayscale.dark2}1A; /* 10% opacity */
    transform: translateY(-1px);
    text-decoration: none;
  }

  &:active {
    transform: translateY(0);
  }
`;

export const ResultContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.gridUnit}px;
  flex: 1;
`;

export const ResultTitle = styled.h5`
  margin: 0;
  font-size: ${({ theme }) => theme.typography.sizes.m}px;
  font-weight: ${({ theme }) => theme.typography.weights.medium};
  color: ${({ theme }) => theme.colors.grayscale.dark2};
  line-height: 1.4;
`;

export const ResultMeta = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.gridUnit}px;
  font-size: ${({ theme }) => theme.typography.sizes.s}px;
  color: ${({ theme }) => theme.colors.grayscale.base};

  span:first-child {
    color: ${({ theme }) => theme.colors.primary.base};
    font-weight: ${({ theme }) => theme.typography.weights.medium};
  }
`;

export const ScoreContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: ${({ theme }) => theme.gridUnit}px;
  min-width: 80px;
`;

export const ScoreValue = styled.div<{ value: number }>`
  font-size: ${({ theme }) => theme.typography.sizes.m}px;
  font-weight: ${({ theme }) => theme.typography.weights.bold};
  color: ${({ theme, value }) =>
    value >= 0.8
      ? theme.colors.success.base
      : value >= 0.6
        ? theme.colors.warning.base
        : theme.colors.error.base};
`;

export const ScoreBar = styled.div<{ score: number }>`
  width: 60px;
  height: 4px;
  background-color: ${({ theme }) => theme.colors.grayscale.light3};
  border-radius: 2px;
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: ${({ score }) => Math.min(score * 100, 100)}%;
    background: linear-gradient(
      90deg,
      ${({ theme }) => theme.colors.error.base} 0%,
      ${({ theme }) => theme.colors.warning.base} 50%,
      ${({ theme }) => theme.colors.success.base} 100%
    );
    border-radius: 3px;
    transition: width 0.3s ease;
  }
`;

export const ScoreLabel = styled.span`
  font-size: ${({ theme }) => theme.typography.sizes.xs}px;
  color: ${({ theme }) => theme.colors.grayscale.base};
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

export const EmptyStateWrapper = styled.div`
  height: 40%;
`;
