import { bootstrapData } from 'src/preamble';
import Icons from 'src/components/Icons';
import {
  BusinessDomain,
  Dashboard,
  DashboardItem,
  Entity,
  EntityType,
  Folder,
  FolderData,
  FolderSlug,
  isFolderType,
  TreeNodeData,
} from './types';

const locale = bootstrapData?.common?.locale || 'en';

const BUSINESS_UNIT_COLORS: { [key in FolderSlug]?: string } = {
  // eslint-disable-next-line theme-colors/no-literal-colors
  [FolderSlug.GlobalPizza]: '#ff6900',
  // eslint-disable-next-line theme-colors/no-literal-colors
  [FolderSlug.GlobalDrinkit]: '#182da8',
};

const BUSINESS_UNIT_SECONDARY_COLORS: { [key in FolderSlug]?: string } = {
  // eslint-disable-next-line theme-colors/no-literal-colors
  [FolderSlug.GlobalPizza]: '#fff2e6',
  // eslint-disable-next-line theme-colors/no-literal-colors
  [FolderSlug.GlobalDrinkit]: '#e8f0ff',
};

export interface HighlightInfo {
  text: string;
  searchTerm: string;
  hasMatch: boolean;
}

export const createHighlightInfo = (
  text: string,
  searchTerm: string,
): HighlightInfo => ({
  text,
  searchTerm,
  hasMatch: searchTerm
    ? text.toLowerCase().includes(searchTerm.toLowerCase())
    : false,
});

export const buildTreeData = (
  entities: Entity[],
  locale: string,
  editMode: boolean,
): TreeNodeData[] =>
  entities.map((entity, index) => ({
    key: `${entity.item_type}-${entity.id}-${entity.parent_id}-${index}`,
    title:
      locale === 'ru'
        ? entity.name_ru || entity.name_en
        : entity.name_en || entity.name_ru,
    entity,
    id: entity.id,
    item_type: entity.item_type,
    selectable: entity.item_type === EntityType.Folder && editMode,
    isLeaf: entity.item_type === EntityType.Dashboard,
    children: isFolderType(entity)
      ? buildTreeData(entity.children || [], locale, editMode)
      : [],
  }));

export const filterTreeData = (
  data: TreeNodeData[],
  searchTerm: string,
): TreeNodeData[] => {
  if (!searchTerm) return data;

  const matchesSearch = (node: TreeNodeData) =>
    node.entity.name_ru?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    node.entity.name_en?.toLowerCase().includes(searchTerm.toLowerCase());

  const filterNode = (node: TreeNodeData): TreeNodeData | null => {
    if (isFolderType(node.entity) && matchesSearch(node)) {
      return {
        ...node,
        searchTerm,
      };
    }

    if (isFolderType(node.entity)) {
      const filteredChildren = node.children
        ?.map(filterNode)
        .filter(Boolean) as TreeNodeData[];

      if (filteredChildren.length > 0) {
        return {
          ...node,
          searchTerm,
          children: filteredChildren,
        };
      }
      return null;
    }

    if (matchesSearch(node)) {
      return {
        ...node,
        searchTerm,
      };
    }

    return null;
  };

  return data.map(filterNode).filter(Boolean) as TreeNodeData[];
};

export const removeEntityFromData = (
  entities: Entity[],
  entityType: EntityType,
  entityId: number,
): Entity[] =>
  entities
    .filter(
      entity => !(entity.item_type === entityType && entity.id === entityId),
    )
    .map(entity => {
      if (entity.item_type === EntityType.Folder && entity.children) {
        return {
          ...entity,
          children: removeEntityFromData(entity.children, entityType, entityId),
        };
      }
      return entity;
    });

export const updateFolderInData = (
  entities: Entity[],
  updatedFolder: Folder,
): Entity[] =>
  entities.map(entity => {
    if (
      entity.id === updatedFolder.id &&
      entity.item_type === EntityType.Folder
    ) {
      return updatedFolder;
    }
    if (entity.item_type === EntityType.Folder && entity.children) {
      return {
        ...entity,
        children: updateFolderInData(entity.children, updatedFolder),
      };
    }
    return entity;
  });

export const findFolderById = (
  entities: Entity[],
  folderId: number,
): Folder | null => {
  for (let i = 0; i < entities.length; i += 1) {
    const entity = entities[i];

    if (entity.id === folderId && entity.item_type === EntityType.Folder) {
      return entity;
    }

    if (entity.item_type === EntityType.Folder && entity.children) {
      const found = findFolderById(entity.children, folderId);
      if (found) {
        return found;
      }
    }
  }
  return null;
};

export const reduceContentMap = (
  contentMap: Record<number, Entity[]>,
  targetFolderId: number,
  action: 'add' | 'remove' | 'update',
  entities: Entity[],
) => {
  if (action === 'add') {
    return {
      ...contentMap,
      [targetFolderId]: [...contentMap[targetFolderId], ...entities],
    };
  }
  if (action === 'remove') {
    const entityToDelete = entities[0];

    return {
      ...contentMap,
      [targetFolderId]: contentMap[targetFolderId].filter(
        item =>
          !(
            item.id === entityToDelete.id &&
            item.item_type === entityToDelete.item_type
          ),
      ),
    };
  }
  return {
    ...contentMap,
    [targetFolderId]: entities,
  };
};

// Data transformation utilities for integration with existing system
export const transformEntityToDashboardItem = (
  dashboard: Dashboard,
  favoriteStatus: Record<number, boolean>,
): DashboardItem | null => ({
  id: dashboard.id,
  name_ru: dashboard.name_ru,
  name_en: dashboard.name_en,
  changedOn: dashboard.changed_on_delta_humanized,
  published: dashboard.published,
  isFavorite: favoriteStatus[dashboard.id] ?? false,
  certifiedBy: dashboard.certified_by,
  owners: dashboard.owners,
  tags: dashboard.tags.filter(tag => tag.type === 1 || tag.type === 5),
});

export const transformFolderToFolderData = (
  folder: Folder,
  favoriteStatus: Record<number, boolean>,
): FolderData => {
  const dashboards =
    (folder.children
      ?.filter(child => child.item_type === EntityType.Dashboard)
      .map((dashboard: Dashboard) =>
        transformEntityToDashboardItem(dashboard, favoriteStatus),
      )
      .filter(Boolean) as DashboardItem[]) || [];

  const subfolders =
    folder.children
      ?.filter(isFolderType)
      .map(child => transformFolderToFolderData(child, favoriteStatus)) || [];

  return {
    id: folder.id.toString(),
    title:
      locale === 'ru'
        ? folder.name_ru || folder.name_en
        : folder.name_en || folder.name_ru,
    subtitle:
      locale === 'ru'
        ? folder.description_ru || folder.description_en
        : folder.description_en || folder.description_ru,
    dashboardCount:
      dashboards.length +
      subfolders.reduce((sum, sf) => sum + sf.dashboardCount, 0),
    icon: Icons.BarChartOutlined, // Default icon
    tags: ['Folder'],
    category: 'general',
    dashboards,
    subfolders: subfolders.length > 0 ? subfolders : undefined,
    isExpanded: false,
  };
};

export const createBusinessDomainFromFolders = (
  globalFolder: Folder | null,
  favoriteStatus: Record<number, boolean>,
): BusinessDomain[] => {
  const businessUnits: BusinessDomain[] = [];

  if (globalFolder) {
    globalFolder.children.forEach(child => {
      if (child.item_type !== EntityType.Folder) return;

      const folders: FolderData[] = [];
      folders.push(
        ...child.children
          .filter(isFolderType)
          .map(folder => transformFolderToFolderData(folder, favoriteStatus)),
      );

      businessUnits.push({
        id: child.id.toString(),
        name:
          locale === 'ru'
            ? child.name_ru || child.name_en
            : child.name_en || child.name_ru,
        color:
          BUSINESS_UNIT_COLORS[child.slug as FolderSlug] ||
          bootstrapData?.common?.colors?.primary.base,
        secondaryColor:
          BUSINESS_UNIT_SECONDARY_COLORS[child.slug as FolderSlug] ||
          bootstrapData?.common?.colors?.primary.light4,
        folders,
      });
    });
    return businessUnits;
  }

  return [];
};

export const extractDashboardIds = (folder: Folder | null): number[] => {
  if (!folder) return [];

  const dashboardIdsSet = new Set<number>([]);
  const extractIds = (entities: Entity[]) => {
    entities.forEach(entity => {
      if (entity.item_type === EntityType.Dashboard) {
        dashboardIdsSet.add(entity.id);
      }
      if (entity.item_type === EntityType.Folder && entity.children) {
        extractIds(entity.children);
      }
    });
  };
  extractIds(folder.children);
  return Array.from(dashboardIdsSet);
};

export const searchDashboardsInFolders = (
  folders: FolderData[],
  query: string,
): DashboardItem[] => {
  const results: DashboardItem[] = [];
  const searchTerm = query.toLowerCase();

  const searchInFolder = (folder: FolderData) => {
    if (folder.dashboards) {
      folder.dashboards.forEach((dashboard: DashboardItem) => {
        const nameEn = dashboard.name_en?.toLowerCase() || '';
        const nameRu = dashboard.name_ru?.toLowerCase() || '';
        if (nameEn.includes(searchTerm) || nameRu.includes(searchTerm)) {
          results.push(dashboard);
        }
      });
    }

    if (folder.subfolders) {
      folder.subfolders.forEach(searchInFolder);
    }
  };

  folders.forEach(searchInFolder);
  return results;
};
