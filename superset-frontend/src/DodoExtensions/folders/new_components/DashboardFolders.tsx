import { useState, useEffect, Fragment, useMemo } from 'react';
import { styled, t } from '@superset-ui/core';
import Icons from 'src/components/Icons';
import BusinessUnitSelector from './BusinessDomainSelector';
import BreadcrumbNavigation from './BreadcrumbNavigation';
import FoldersSection from './FoldersSection';
import DashboardsSection from './DashboardsSection';
import Folder from './Folder';
import { BusinessDomain, FolderData } from '../types';
import DashboardSearch from './DashboardSearch';
import { searchDashboardsInFolders } from '../utils';

interface DashboardFoldersProps {
  domainData: BusinessDomain[];
  canEditFolder: boolean;
  saveFavoriteStatus: (id: number, isStarred: boolean) => void;
  onDeleteDashboard?: (dashboard: any) => void;
  onExportDashboard?: (dashboard: any) => void;
  onEditDashboard?: (dashboard: any) => void;
}

const MainContainer = styled.div`
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: ${({ theme }) => theme.colors.grayscale.light4};
`;

const ContentArea = styled.div`
  flex: 1;
  display: flex;
  background-color: ${({ theme }) => theme.colors.grayscale.light4};
`;

const Sidebar = styled.div`
  width: 280px;
  background-color: ${({ theme }) => theme.colors.grayscale.light5};
  border-right: 1px solid ${({ theme }) => theme.colors.grayscale.light2};
  display: flex;
  flex-direction: column;
`;

const FolderList = styled.div`
  flex: 1;
  overflow-y: auto;
`;

const MainContent = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
`;

const Header = styled.div`
  background-color: ${({ theme }) => theme.colors.grayscale.light5};
  min-height: 131px;
`;

const DashboardGrid = styled.div`
  flex: 1;
  padding: ${({ theme }) => theme.gridUnit * 6}px;
  overflow-y: auto;
  background-color: ${({ theme }) => theme.colors.grayscale.light4};
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  text-align: center;
`;

const EmptyStateIcon = styled.div`
  width: 80px;
  height: 80px;
  background-color: ${({ theme }) => theme.colors.grayscale.light4};
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: ${({ theme }) => theme.gridUnit * 4}px;
  border: 2px dashed ${({ theme }) => theme.colors.grayscale.light2};
`;

const EmptyStateIconContent = styled(Icons.FolderOutlined)`
  font-size: 32px;
  color: ${({ theme }) => theme.colors.text.label};
`;

const EmptyStateTitle = styled.span`
  color: ${({ theme }) => theme.colors.text.label};
  margin-bottom: ${({ theme }) => theme.gridUnit * 2}px;
`;

const EmptyStateText = styled.span`
  font-size: ${({ theme }) => theme.typography.sizes.m}px;
  color: ${({ theme }) => theme.colors.text.label};
`;

const findFolderById = (
  folderId: string,
  folders: any[],
): FolderData | null => {
  for (const folder of folders) {
    if (folder.id === folderId) {
      return folder;
    }
    if (folder.subfolders) {
      const found = findFolderById(folderId, folder.subfolders);
      if (found) return found;
    }
  }
  return null;
};

const DashboardFolders = ({
  domainData,
  canEditFolder,
  saveFavoriteStatus,
  onDeleteDashboard,
  onExportDashboard,
  onEditDashboard,
}: DashboardFoldersProps) => {
  const [selectedDomain, setSelectedDomain] = useState(
    domainData[0]?.id || 'pizza',
  );
  const [selectedFolder, setSelectedFolder] = useState('');
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(
    new Set(),
  );
  const [isSearching, setIsSearching] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const currentDomain =
    domainData.find(b => b.id === selectedDomain) || domainData[0];
  const currentFolders = useMemo(
    () => currentDomain?.folders || [],
    [currentDomain],
  );

  const toggleFolderExpansion = (folderId: string) => {
    setExpandedFolders(prev => {
      const newSet = new Set(prev);
      if (newSet.has(folderId)) {
        newSet.delete(folderId);
      } else {
        newSet.add(folderId);
      }
      return newSet;
    });
  };

  const handleBreadcrumbClick = (folderId?: string) => {
    if (folderId) {
      setSelectedFolder(folderId);
    } else {
      setSelectedFolder('');
    }
  };

  const searchResults = useMemo(() => {
    if (!searchQuery.trim() || isSearching) return [];
    return searchDashboardsInFolders(currentFolders, searchQuery);
  }, [searchQuery, currentFolders, isSearching]);

  useEffect(() => {
    if (currentFolders.length > 0 && !selectedFolder) {
      setSelectedFolder(currentFolders[0].id);
    }
  }, [selectedDomain, currentFolders, selectedFolder]);

  const totalDashboards = currentFolders.reduce(
    (sum, folder) => sum + folder.dashboardCount,
    0,
  );
  const currentFolder = findFolderById(selectedFolder, currentFolders);

  const dashboards = currentFolder?.dashboards || [];
  const subfolders = currentFolder?.subfolders || [];

  // Recursive function to render folders and subfolders (max depth: 5)
  const renderFolder = (folder: FolderData, level = 0) => {
    if (level >= 5) {
      return null;
    }

    const isSelected = isSearching ? false : selectedFolder === folder.id;
    const businessColor = currentDomain.color;
    const lightBusinessColor = currentDomain.secondaryColor;
    const isExpanded = expandedFolders.has(folder.id);
    // Only show expand if not at max depth
    const hasSubfolders = Boolean(
      folder.subfolders && folder.subfolders.length > 0 && level < 4,
    );
    const indentLevel = level * 16;

    return (
      <Fragment key={folder.id}>
        {/* Main folder item */}
        <Folder
          folderId={folder.id}
          folderTitle={folder.title}
          folderSubtitle={folder.subtitle}
          folderDashboardCount={folder.dashboardCount}
          isSelected={isSelected}
          businessColor={businessColor}
          lightBusinessColor={lightBusinessColor}
          indentLevel={indentLevel}
          hasSubfolders={hasSubfolders}
          isExpanded={isExpanded}
          toggleFolderExpansion={toggleFolderExpansion}
          setSelectedFolder={isSearching ? () => {} : setSelectedFolder}
        />

        {/* Render subfolders if expanded */}
        {hasSubfolders && isExpanded && (
          <div>
            {folder?.subfolders?.map((subfolder: any) =>
              renderFolder(subfolder, level + 1),
            )}
          </div>
        )}
      </Fragment>
    );
  };

  return (
    <MainContainer>
      <ContentArea>
        <Sidebar>
          <BusinessUnitSelector
            domainData={domainData}
            selectedDomain={selectedDomain}
            onBusinessChange={(businessId: string) => {
              setSelectedDomain(businessId);
              setSelectedFolder('');
            }}
            totalDashboards={totalDashboards}
            currentBusiness={currentDomain}
            canEditFolder={canEditFolder}
          />

          <FolderList>
            {currentFolders.map(folder => renderFolder(folder, 0))}
          </FolderList>
        </Sidebar>

        <MainContent>
          <Header>
            <DashboardSearch
              currentBusiness={currentDomain}
              searchQuery={searchQuery}
              setSearchQuery={setSearchQuery}
              setIsSearching={setIsSearching}
            />
            {!isSearching && (
              <BreadcrumbNavigation
                currentBusiness={currentDomain}
                selectedFolder={selectedFolder}
                currentFolders={currentFolders}
                onBreadcrumbClick={handleBreadcrumbClick}
              />
            )}
          </Header>

          <DashboardGrid>
            {isSearching ? (
              <>
                {searchResults.length > 0 ? (
                  <DashboardsSection
                    dashboards={searchResults}
                    currentBusiness={currentDomain}
                    saveFavoriteStatus={saveFavoriteStatus}
                    onDeleteDashboard={onDeleteDashboard}
                    onExportDashboard={onExportDashboard}
                    onEditDashboard={onEditDashboard}
                    isSearchResults
                  />
                ) : (
                  <EmptyState>
                    <EmptyStateIcon>
                      <EmptyStateIconContent />
                    </EmptyStateIcon>
                    <EmptyStateTitle>
                      {t('No dashboards found')}
                    </EmptyStateTitle>
                    <EmptyStateText>
                      {t(
                        'No dashboards match your search query "%s"',
                        searchQuery,
                      )}
                    </EmptyStateText>
                  </EmptyState>
                )}
              </>
            ) : !currentFolder ? (
              <EmptyState>
                <EmptyStateIcon>
                  <EmptyStateIconContent />
                </EmptyStateIcon>
                <EmptyStateTitle>
                  {t('Select a folder to view dashboards')}
                </EmptyStateTitle>
                <EmptyStateText>
                  {t(
                    'Choose a folder from the left panel to see its dashboards and subfolders',
                  )}
                </EmptyStateText>
              </EmptyState>
            ) : (
              <div>
                <FoldersSection
                  subfolders={subfolders}
                  currentBusiness={currentDomain}
                  onFolderClick={setSelectedFolder}
                />

                <DashboardsSection
                  dashboards={dashboards}
                  currentBusiness={currentDomain}
                  saveFavoriteStatus={saveFavoriteStatus}
                  onDeleteDashboard={onDeleteDashboard}
                  onExportDashboard={onExportDashboard}
                  onEditDashboard={onEditDashboard}
                />

                {subfolders.length === 0 && dashboards.length === 0 ? (
                  <EmptyState>
                    <EmptyStateIcon>
                      <EmptyStateIconContent />
                    </EmptyStateIcon>
                    <EmptyStateTitle>
                      {t('The folder is empty')}
                    </EmptyStateTitle>
                    <EmptyStateText>
                      {t(
                        'This folder does not contain any dashboards or subfolders.',
                      )}
                    </EmptyStateText>
                  </EmptyState>
                ) : null}
              </div>
            )}
          </DashboardGrid>
        </MainContent>
      </ContentArea>
    </MainContainer>
  );
};

export default DashboardFolders;
