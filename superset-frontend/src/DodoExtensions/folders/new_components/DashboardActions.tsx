import { styled, t } from '@superset-ui/core';
import { Tooltip } from 'src/components/Tooltip';
import Icons from 'src/components/Icons';
import ConfirmStatusChange from 'src/components/ConfirmStatusChange';
import { DashboardItem } from '../types';

interface DashboardActionsProps {
  dashboard: DashboardItem;
  onDeleteDashboard?: (dashboard: DashboardItem) => void;
  onExportDashboard?: (dashboard: DashboardItem[]) => void;
  onEditDashboard?: (dashboard: DashboardItem) => void;
}

const ActionsContainer = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.gridUnit * 2}px;
`;

const ActionIconsGroup = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.gridUnit * 1.5}px;
`;

const ActionIconButton = styled.div`
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: ${({ theme }) => theme.borderRadius - 1}px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: ${({ theme }) => theme.colors.grayscale.light3};
  }
`;

const DeleteActionIcon: React.FC<{
  dashboard: DashboardItem;
  onDelete: (dashboard: DashboardItem) => void;
}> = ({ dashboard, onDelete }) => (
  <ConfirmStatusChange
    title={t('Please confirm')}
    description={
      <>
        {t('Are you sure you want to delete')}{' '}
        <b>{dashboard.name_ru || dashboard.name_en}</b>?
      </>
    }
    onConfirm={() => onDelete?.(dashboard)}
  >
    {confirmDelete => (
      <Tooltip
        id="delete-action-tooltip"
        title={t('Delete')}
        placement="bottom"
      >
        <ActionIconButton
          onClick={e => {
            e.stopPropagation();
            confirmDelete();
          }}
        >
          <Icons.Trash iconSize="l" />
        </ActionIconButton>
      </Tooltip>
    )}
  </ConfirmStatusChange>
);

const DashboardActions: React.FC<DashboardActionsProps> = ({
  dashboard,
  onDeleteDashboard,
  onExportDashboard,
  onEditDashboard,
}) => (
  <ActionsContainer>
    <ActionIconsGroup>
      {onDeleteDashboard && (
        <DeleteActionIcon dashboard={dashboard} onDelete={onDeleteDashboard} />
      )}
      {onExportDashboard && (
        <Tooltip
          id="export-action-tooltip"
          title={t('Export')}
          placement="bottom"
        >
          <ActionIconButton
            onClick={e => {
              e.stopPropagation();
              onExportDashboard([dashboard]);
            }}
          >
            <Icons.Share iconSize="l" />
          </ActionIconButton>
        </Tooltip>
      )}
      {onEditDashboard && (
        <Tooltip id="edit-action-tooltip" title={t('Edit')} placement="bottom">
          <ActionIconButton
            onClick={e => {
              e.stopPropagation();
              onEditDashboard(dashboard);
            }}
          >
            <Icons.EditAlt iconSize="l" />
          </ActionIconButton>
        </Tooltip>
      )}
    </ActionIconsGroup>
  </ActionsContainer>
);

export default DashboardActions;
