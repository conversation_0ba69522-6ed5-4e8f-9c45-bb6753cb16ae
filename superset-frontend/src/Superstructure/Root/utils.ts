import { Entity, EntityType, Folder } from 'src/DodoExtensions/folders/types';
import { GET_LOGIN_TOKEN, GET_CSRF_TOKEN } from '../api/init';
import { MESSAGES } from '../constants';
import {
  InitializedResponse,
  MicrofrontendNavigation,
  DashboardConfig,
} from '../types/global';
import {
  handleAxiosError,
  handleCorrectCaseReturn,
  handleDefaultCaseReturn,
} from './helpers';

// const dirtyHackDodoIs = () => {
//   // In dodois the div.all has css property min-height, that forces the footer to be overlapped
//   const dodoElementAll = document.getElementsByClassName('all')[0];

//   if (dodoElementAll?.classList.contains('overwrite-height')) {
//     dodoElementAll.classList.remove('overwrite-height');
//   }
// };

const getLoginToken = async (): Promise<
  InitializedResponse<{ access_token: string } | null>
> => {
  const loginResponse = await GET_LOGIN_TOKEN();

  if ('code' in loginResponse) {
    return handleAxiosError({
      response: loginResponse,
      errorObject: MESSAGES.LOGIN,
    });
  }

  if ('access_token' in loginResponse) {
    return handleCorrectCaseReturn<{ access_token: string }>({
      response: loginResponse,
      errorObject: MESSAGES.LOGIN,
    });
  }

  return handleDefaultCaseReturn({
    errorObject: MESSAGES.LOGIN,
    errorMessage: 'NO_TOKEN',
  });
};

const getCsrfToken = async ({
  useAuth = true,
}): Promise<InitializedResponse<{ result: string } | null>> => {
  const csrfResponse = await GET_CSRF_TOKEN({ useAuth });

  if ('code' in csrfResponse) {
    const csrfResponseAltered = {
      ...csrfResponse,
      message:
        'Проверьте, что в Вашей учетной записи Dodo IS заполнены e-mail, имя и фамилия. При отсутствии этих данных, авторизация в сервисе невозможна',
    };
    return handleAxiosError({
      response: csrfResponseAltered,
      errorObject: MESSAGES.CSRF,
    });
  }

  if ('result' in csrfResponse) {
    return handleCorrectCaseReturn<{ result: string }>({
      response: csrfResponse,
      errorObject: MESSAGES.CSRF,
    });
  }

  return handleDefaultCaseReturn({
    errorObject: MESSAGES.CSRF,
    errorMessage: 'NO_TOKEN',
  });
};

const getRoutes = (
  navigation: MicrofrontendNavigation,
  pluginFolder: Folder | null,
) => {
  const hardcodedDashboards: DashboardConfig[] = Object.values(
    navigation.dashboards || {},
  );
  if (hardcodedDashboards.length) {
    return hardcodedDashboards.map(dashboard => dashboard.idOrSlug);
  }

  if (!pluginFolder) {
    return null;
  }
  const extractRoutesFromFolder = (pluginFolder: Folder) => {
    const routes: number[] = [];

    pluginFolder?.children.forEach(entity => {
      if (entity.item_type === EntityType.Dashboard) {
        routes.push(entity.id);
      }
      if (
        entity.item_type === EntityType.Folder &&
        entity.children.length > 0
      ) {
        routes.push(...extractRoutesFromFolder(entity));
      }
    });
    return routes;
  };
  return extractRoutesFromFolder(pluginFolder);
};

const createFolderContentFromHardcodedDashboards = (
  navigation: MicrofrontendNavigation,
): Entity[] => {
  const hardcodedDashboards: DashboardConfig[] = Object.values(
    navigation.dashboards || {},
  );
  if (hardcodedDashboards.length) {
    return hardcodedDashboards.map(dashboard => ({
      id: dashboard.idOrSlug as number,
      name_ru: dashboard.nameRU,
      name_en: dashboard.name,
      certified_by: 'Dodo IS',
      parent_id: 0,
      item_type: EntityType.Dashboard,
      owners: [],
      changed_on_delta_humanized: '',
      published: true,
      tags: [],
    }));
  }
  return [];
};

export {
  getLoginToken,
  getCsrfToken,
  // dirtyHackDodoIs,
  getRoutes,
  createFolderContentFromHardcodedDashboards,
};
