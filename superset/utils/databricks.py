"""
Databricks API client for handling authentication and requests.
This module manages token refresh and provides a clean interface for making
authenticated requests to Databricks endpoints.
"""

import logging
import time
from typing import Any

import requests
from flask import current_app

from superset.utils import json

logger = logging.getLogger(__name__)


class DatabricksClient:
    """
    Client for interacting with Databricks APIs with automatic token refresh.
    """

    def __init__(self) -> None:
        self._access_token = None
        self._token_expiry = 0
        self._refresh_margin = 300

    def _refresh_token(self) -> bool:
        """
        Refresh the access token using client credentials.

        Returns:
            bool: True if token refresh was successful, False otherwise.
        """
        try:
            client_id = current_app.config.get("DATABRICKS_CLIENT_ID")
            client_secret = current_app.config.get("DATABRICKS_CLIENT_SECRET")
            endpoint_id = "1a1d45f3699d4aaa85e38e037d90dee3"
            workplace_id = "adb-5812797581086196.16"

            if not client_id or not client_secret:
                logger.error("Databricks credentials not configured")
                return False

            # TODO: different endpoints
            authorization_details = [
                {
                    "type": "workspace_permission",
                    "object_type": "serving-endpoints",
                    "object_path": f"/serving-endpoints/{endpoint_id}",
                    "actions": ["query_inference_endpoint"],
                }
            ]

            payload = {
                "grant_type": "client_credentials",
                "scope": "all-apis",
                "authorization_details": json.dumps(authorization_details),
            }

            response = requests.post(
                f"https://{workplace_id}.azuredatabricks.net/oidc/v1/token",
                auth=(client_id, client_secret),
                data=payload,
                timeout=30,
            )

            if not response.ok:
                logger.error(
                    "Failed to refresh token: %s %s",
                    response.status_code,
                    response.text,
                )
                return False

            token_data = response.json()
            self._access_token = token_data.get("access_token")
            expires_in = token_data.get("expires_in", 3600)

            if not self._access_token:
                logger.error("access_token not found in response")
                return False

            self._token_expiry = time.time() + expires_in - self._refresh_margin
            return True

        except Exception as ex:  # pylint: disable=broad-exception-caught
            logger.exception("Error refreshing Databricks token: %s", str(ex))
            return False

    def _ensure_valid_token(self) -> bool:
        """
        Ensure we have a valid token, refreshing if necessary.

        Returns:
            bool: True if a valid token is available, False otherwise.
        """
        if not self._access_token or time.time() > self._token_expiry:
            return self._refresh_token()
        return True

    def get_headers(self) -> dict[str, str]:
        """
        Get headers for API requests including authorization.

        Returns:
            dict[str, str]: Headers dictionary with Authorization if available
        """
        headers = {"Content-Type": "application/json"}

        if self._ensure_valid_token():
            headers["Authorization"] = f"Bearer {self._access_token}"

        return headers

    def post(self, url: str, json_data: dict[str, Any]) -> requests.Response:
        """
        Make a POST request to a Databricks endpoint with authentication.

        Args:
            url: The URL to call.
            json_data: JSON data to send in the request body.

        Returns:
            requests.Response: The response from the API.
        """
        headers = self.get_headers()

        return requests.post(url, json=json_data, headers=headers, timeout=30)

    def ai_search(self, query: str, num: int = 3) -> requests.Response:
        """
        Make a POST request to the AI search endpoint.

        Args:
            query: The query to search for.
            num: The number of results to return.

        Returns:
            requests.Response: The response from the API.
        """
        return self.post(
            url="https://1a1d45f3699d4aaa85e38e037d90dee3.2.serving.azuredatabricks.net/5812797581086196/serving-endpoints/data-bot-search-prod/invocations",
            json_data={"inputs": {"query": query, "top_k": num}},
        )
