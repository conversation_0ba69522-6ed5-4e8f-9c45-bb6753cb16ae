import logging
from typing import Any

from superset.orgstructure.models import (
    ClaimType,
    UnitSchema,
    UnitType,
    UnitTypeHeaders,
)

logger = logging.getLogger(__name__)


def parse_claim(claim: str) -> tuple[str, ClaimType] | None:
    try:
        parts = claim.split(":")
        uuid = parts[0]
        claim_type = ClaimType(int(parts[1]))
        return uuid, claim_type
    except Exception:  # pylint: disable=broad-exception-caught
        return None


def parse_unit(unit_type_header: str, data: dict[str, Any]) -> dict[str, Any] | None:
    """
    Parse and validate unit data from Kafka message using the schema.

    Args:
        unit_type_header: The Event-Type header value from the Kafka message
        data: The message payload

    Returns:
        Validated unit data from schema or None if validation fails
    """
    try:
        if unit_type_header == UnitTypeHeaders.UNIT.value:
            unit_type = data.get("Type")
        elif unit_type_header == UnitTypeHeaders.PIZZERIA.value:
            unit_type = UnitType.PIZZERIA.value
        elif unit_type_header == UnitTypeHeaders.PRODUCTION_DISTRIBUTION_WORKSHOP.value:
            unit_type = UnitType.PRODUCTION_DISTRIBUTION_WORKSHOP.value
        else:
            return None

        logger.debug("Processing message with type %s", unit_type_header)

        # Update the data with the calculated unit type
        data["Type"] = unit_type

        # Validate and return the schema object directly
        # The schema will handle all data transformations and validation
        return UnitSchema().load(data)

    except Exception as ex:  # pylint: disable=broad-exception-caught
        logger.exception("parse_unit failed: %s", ex)
        return None
