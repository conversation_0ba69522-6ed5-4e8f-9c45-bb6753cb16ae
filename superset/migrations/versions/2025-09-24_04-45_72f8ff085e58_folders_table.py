# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
"""Folders table

Revision ID: 72f8ff085e58
Revises: cb26e94071b1
Create Date: 2025-09-24 04:45:20.992480

"""

import random

import sqlalchemy as sa
from alembic import op
from flask_appbuilder.security.sqla.models import User
from sqlalchemy.orm import Session

from superset.folders.models import (
    Folder,
    FolderItem,
    FolderItemType,
    FolderLevel,
    FolderType,
)
from superset.models.dashboard import Dashboard

# revision identifiers, used by Alembic.
revision = "72f8ff085e58"
down_revision = "cb26e94071b1"

GLOBAL_DASHBOARDS = [
    (
        "Операционные метрики",
        "Operational metrics",
        "Ключевые показатели работы",
        "Key performance indicators",
        [
            975,
            953,
            190,
            111,
            844,
            612,
            606,
            714,
            219,
            197,
            613,
            812,
            750,
            761,
            220,
            252,
            667,
        ],
    ),
    (
        "Рейтинги",
        "Ratings",
        "Сравнение и ранжирование",
        "Comparison and ranking",
        [977, 1079, 280, 294, 181],
    ),
    (
        "Продукт",
        "Product",
        "Показатели продукта",
        "Product performance",
        [931, 278, 255, 249, 556, 240, 201],
    ),
    (
        "Команда",
        "Team",
        "Эффективность команды",
        "Team efficiency",
        [813, 645, 67, 269, 733, 697, 212],
    ),
    (
        "Маркетинг",
        "Marketing",
        "Метрики маркетинга",
        "Marketing metrics",
        [279, 178, 776, 272, 868, 880],
    ),
    (
        "Анти-фрод",
        "Anti-fraud",
        "Показатели защиты",
        "Fraud prevention metrics",
        [78, 622, 694],
    ),
    (
        "Себестоимость",
        "Cost price",
        "Издержки и расходы",
        "Costs and expenses",
        [235, 260, 244, 250, 146, 66, 1006],
    ),
    ("Прочее", "Miscellaneous", "Прочие метрики", "Other metrics", [215]),
    (
        "Технические дашборды",
        "Technical dashboards",
        "Технические показатели",
        "Technical indicators",
        [1151, 833, 1150],
    ),
]

PLUGIN_DASHBOARDS = [
    (
        "MAIN",
        "MAIN",
        "MAIN",
        "MAIN",
        [1098, 1138, 975, 977, 1079, 280, 645, 844, 279, 931, 868, 953],
    ),
    (
        "ОПЕРАЦИОННЫЕ МЕТРИКИ",
        "ОПЕРАЦИОННЫЕ МЕТРИКИ",
        "ОПЕРАЦИОННЫЕ МЕТРИКИ",
        "ОПЕРАЦИОННЫЕ МЕТРИКИ",
        [606, 714, 612, 613, 197, 252, 219],
    ),
    ("АНТИ-ФРОД", "АНТИ-ФРОД", "АНТИ-ФРОД", "АНТИ-ФРОД", [78, 622, 694]),
    ("КОМАНДА", "КОМАНДА", "КОМАНДА", "КОМАНДА", [67, 813, 697, 733, 212]),
    ("ПРОДУКТ", "ПРОДУКТ", "ПРОДУКТ", "ПРОДУКТ", [278, 556, 249, 255, 240]),
    (
        "СЕБЕСТОИМОСТЬ",
        "СЕБЕСТОИМОСТЬ",
        "СЕБЕСТОИМОСТЬ",
        "СЕБЕСТОИМОСТЬ",
        [235, 244, 66],
    ),
    ("МАРКЕТИНГ", "МАРКЕТИНГ", "МАРКЕТИНГ", "МАРКЕТИНГ", [178, 880, 272, 776]),
    ("РЕЙТИНГИ", "РЕЙТИНГИ", "РЕЙТИНГИ", "РЕЙТИНГИ", [294, 181]),
    ("ПРОЧЕЕ", "ПРОЧЕЕ", "ПРОЧЕЕ", "ПРОЧЕЕ", [215, 1137]),
]


DRINKIT_DASHBOARDS = [
    (
        "Company Overview",
        "Company Overview",
        "Company Overview",
        "Company Overview",
        [949],
    ),
    (
        "B2C Analytics",
        "B2C Analytics",
        "App, Assortment, Loyalty, Marketing",
        "App, Assortment, Loyalty, Marketing",
        [136, 980, 139, 251, 226, 192, 523],
    ),
    (
        "Shop Operations",
        "Shop Operations",
        "Baristas, Speed, Quality",
        "Baristas, Speed, Quality",
        [728, 1068, 507, 667, 888],
    ),
    (
        "Controlling & Guest Feedback",
        "Controlling & Guest Feedback",
        "Controlling & Guest Feedback",
        "Controlling & Guest Feedback",
        [981, 1080, 769],
    ),
    (
        "Supply & Demand Planning",
        "Supply & Demand Planning",
        "Supply & Demand Planning",
        "Supply & Demand Planning",
        [265, 227, 801],
    ),
]


def add_dashboards_to_folder(session: Session, folder: Folder, dashboards: list[int]):
    for i, dashboard_id in enumerate(dashboards):
        if (
            dashboard := session.query(Dashboard)
            .filter_by(id=dashboard_id)
            .one_or_none()
        ):
            folder_item = FolderItem(
                folder_id=folder.id,
                item_id=dashboard.id,
                item_type=FolderItemType.DASHBOARD.value,
                order=i,
            )
            session.add(folder_item)


def upgrade():
    op.create_table(
        "folders",
        sa.Column("id", sa.Integer, primary_key=True),
        sa.Column("name_ru", sa.String, nullable=False),
        sa.Column("name_en", sa.String, nullable=False),
        sa.Column("description_ru", sa.String, nullable=False),
        sa.Column("description_en", sa.String, nullable=False),
        sa.Column("type", sa.Integer, nullable=False),
        sa.Column("level", sa.Integer, nullable=False),
        sa.Column("slug", sa.String, nullable=True),
        sa.Column("order", sa.Integer, nullable=False, default=0),
        sa.Column("team_id", sa.Integer, nullable=True),
        sa.Column("parent_id", sa.Integer, nullable=True),
        sa.Column("created_on", sa.DateTime, nullable=True),
        sa.Column("changed_on", sa.DateTime, nullable=True),
        sa.Column("created_by_fk", sa.Integer, nullable=True),
        sa.Column("changed_by_fk", sa.Integer, nullable=True),
        sa.ForeignKeyConstraint(["created_by_fk"], ["ab_user.id"]),
        sa.ForeignKeyConstraint(["changed_by_fk"], ["ab_user.id"]),
        sa.ForeignKeyConstraint(["team_id"], ["teams.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["parent_id"], ["folders.id"], ondelete="CASCADE"),
    )
    op.create_table(
        "folder_items",
        sa.Column("id", sa.Integer, primary_key=True),
        sa.Column("folder_id", sa.Integer, nullable=False),
        sa.Column("item_id", sa.Integer, nullable=False),
        sa.Column("item_type", sa.Integer, nullable=False),
        sa.Column("order", sa.Integer, nullable=False, default=0),
        sa.Column("created_on", sa.DateTime, nullable=True),
        sa.Column("changed_on", sa.DateTime, nullable=True),
        sa.Column("created_by_fk", sa.Integer, nullable=True),
        sa.Column("changed_by_fk", sa.Integer, nullable=True),
        sa.ForeignKeyConstraint(["created_by_fk"], ["ab_user.id"]),
        sa.ForeignKeyConstraint(["changed_by_fk"], ["ab_user.id"]),
        sa.ForeignKeyConstraint(["folder_id"], ["folders.id"], ondelete="CASCADE"),
    )
    op.create_index("idx_folder_items_folder_id", "folder_items", ["folder_id"])
    op.create_index("idx_folder_items_item_id", "folder_items", ["item_id"])
    op.create_index("idx_folder_items_item_type", "folder_items", ["item_type"])

    bind = op.get_bind()
    session = Session(bind=bind)

    # create root folder
    global_folder = Folder(
        name_ru="global",
        name_en="global",
        description_ru="Global folder",
        description_en="Global folder",
        type=FolderType.GLOBAL.value,
        level=FolderLevel.ROOT.value,
        slug="global",
        order=0,
    )

    session.add(global_folder)
    session.flush()

    # create system folders
    personal_folder = Folder(
        name_ru="personal",
        name_en="personal",
        description_ru="Personal folder",
        description_en="Personal folder",
        type=FolderType.PERSONAL.value,
        level=FolderLevel.SYSTEM.value,
        slug="personal",
        order=0,
    )
    team_folder = Folder(
        name_ru="team",
        name_en="team",
        description_ru="Team folder",
        description_en="Team folder",
        type=FolderType.TEAM.value,
        level=FolderLevel.SYSTEM.value,
        slug="team",
        order=1,
    )

    # Global SYSTEM folders
    global_pizza_folder = Folder(
        name_ru="pizza",
        name_en="pizza",
        description_ru="Pizza folder",
        description_en="Pizza folder",
        type=FolderType.GLOBAL.value,
        level=FolderLevel.SYSTEM.value,
        parent_id=global_folder.id,
        slug="global_pizza",
        order=0,
    )
    global_drinkit_folder = Folder(
        name_ru="drinkit",
        name_en="drinkit",
        description_ru="Drinkit folder",
        description_en="Drinkit folder",
        type=FolderType.GLOBAL.value,
        level=FolderLevel.SYSTEM.value,
        parent_id=global_folder.id,
        slug="global_drinkit",
        order=1,
    )

    # Plugin ROOT folder
    plugin_folder = Folder(
        name_ru="plugin",
        name_en="plugin",
        description_ru="Plugin folder",
        description_en="Plugin folder",
        type=FolderType.PLUGIN.value,
        level=FolderLevel.ROOT.value,
        slug="plugin",
        order=2,
    )
    session.add(plugin_folder)
    session.add(personal_folder)
    session.add(team_folder)
    session.add(global_pizza_folder)
    session.add(global_drinkit_folder)
    session.flush()

    plugin_pizza_folder = Folder(
        name_ru="pizza",
        name_en="pizza",
        description_ru="Plugin pizza folder",
        description_en="Plugin pizza folder",
        type=FolderType.PLUGIN.value,
        level=FolderLevel.ROOT.value,
        parent_id=plugin_folder.id,
        slug="plugin_pizza",
        order=0,
    )

    plugin_drinkit_folder = Folder(
        name_ru="drinkit",
        name_en="drinkit",
        description_ru="Plugin drinkit folder",
        description_en="Plugin drinkit folder",
        type=FolderType.PLUGIN.value,
        level=FolderLevel.ROOT.value,
        parent_id=plugin_folder.id,
        order=1,
    )

    session.add(plugin_pizza_folder)
    session.add(plugin_drinkit_folder)
    session.flush()

    plugin_pizza_analytics_folder = Folder(
        name_ru="analytics",
        name_en="analytics",
        description_ru="Plugin pizza analytics folder",
        description_en="Plugin pizza analytics folder",
        type=FolderType.PLUGIN.value,
        level=FolderLevel.SYSTEM.value,
        parent_id=plugin_pizza_folder.id,
        order=0,
    )
    plugin_drinkit_analytics_folder = Folder(
        name_ru="analytics",
        name_en="analytics",
        description_ru="Plugin drinkit analytics folder",
        description_en="Plugin drinkit analytics folder",
        type=FolderType.PLUGIN.value,
        level=FolderLevel.SYSTEM.value,
        parent_id=plugin_drinkit_folder.id,
        order=0,
    )
    session.add(plugin_pizza_analytics_folder)
    session.add(plugin_drinkit_analytics_folder)
    session.flush()

    for i, (name_ru, name_en, description_ru, description_en, dashboards) in enumerate(
        GLOBAL_DASHBOARDS
    ):
        folder = Folder(
            name_ru=name_ru,
            name_en=name_en,
            description_ru=description_ru,
            description_en=description_en,
            type=FolderType.GLOBAL.value,
            level=FolderLevel.USER.value,
            parent_id=global_pizza_folder.id,
            order=i,
        )
        session.add(folder)
        session.flush()
        add_dashboards_to_folder(session, folder, dashboards)

    for i, (name_ru, name_en, description_ru, description_en, dashboards) in enumerate(
        PLUGIN_DASHBOARDS
    ):
        folder = Folder(
            name_ru=name_ru,
            name_en=name_en,
            description_ru=description_ru,
            description_en=description_en,
            type=FolderType.PLUGIN.value,
            level=FolderLevel.USER.value,
            parent_id=plugin_pizza_analytics_folder.id,
            order=i,
        )
        session.add(folder)
        session.flush()
        add_dashboards_to_folder(session, folder, dashboards)

    for i, (name_ru, name_en, description_ru, description_en, dashboards) in enumerate(
        DRINKIT_DASHBOARDS
    ):
        folder = Folder(
            name_ru=name_ru,
            name_en=name_en,
            description_ru=description_ru,
            description_en=description_en,
            type=FolderType.GLOBAL.value,
            level=FolderLevel.USER.value,
            parent_id=global_drinkit_folder.id,
            order=i,
        )
        session.add(folder)
        session.flush()
        add_dashboards_to_folder(session, folder, dashboards)

    for i, (name_ru, name_en, description_ru, description_en, dashboards) in enumerate(
        DRINKIT_DASHBOARDS
    ):
        folder = Folder(
            name_ru=name_ru,
            name_en=name_en,
            description_ru=description_ru,
            description_en=description_en,
            type=FolderType.PLUGIN.value,
            level=FolderLevel.USER.value,
            parent_id=plugin_drinkit_analytics_folder.id,
            order=i,
        )
        session.add(folder)
        session.flush()
        add_dashboards_to_folder(session, folder, dashboards)

    # populate personal user folders
    users = session.query(User.id).all()
    dashboards = session.query(
        Dashboard.id, Dashboard.created_by_fk, Dashboard.certified_by
    ).all()
    certified_dashboards = [
        dashboard.id for dashboard in dashboards if dashboard.certified_by
    ]
    created_by_dash_hash = {}
    for dashboard_id in dashboards:
        if dashboard_id.created_by_fk not in created_by_dash_hash:
            created_by_dash_hash[dashboard_id.created_by_fk] = []
        created_by_dash_hash[dashboard_id.created_by_fk].append(dashboard_id.id)

    personal_user_folders: list[Folder] = []
    for (user_id,) in users:
        personal_user_folder = Folder(
            name_ru="Пример папки",
            name_en="Sample folder",
            description_ru="Пример папки с дашбордами",
            description_en="Sample folder with dashboards",
            type=FolderType.PERSONAL.value,
            level=FolderLevel.USER.value,
            parent_id=personal_folder.id,
            created_by_fk=user_id,
            order=0,
        )
        personal_user_folders.append(personal_user_folder)
    session.add_all(personal_user_folders)
    session.flush()
    print(f"Created {len(personal_user_folders)} personal user folders")
    folder_items = []
    for personal_user_folder in personal_user_folders:
        common_dashboards = []
        created_by_dashboards = []
        if certified_dashboards:
            common_dashboards = random.choices(certified_dashboards, k=3)
        if created_by_dash_hash.get(personal_user_folder.created_by_fk):
            created_by_dashboards = random.choices(
                list(set(created_by_dash_hash[personal_user_folder.created_by_fk])), k=3
            )
        dashboards = list(set(common_dashboards + created_by_dashboards))
        random.shuffle(dashboards)

        for i, dashboard_id in enumerate(dashboards):
            personal_item = FolderItem(
                folder_id=personal_user_folder.id,
                item_id=dashboard_id,
                item_type=FolderItemType.DASHBOARD.value,
                created_by_fk=personal_user_folder.created_by_fk,
                order=i,
            )
            folder_items.append(personal_item)
    print(f"Created {len(folder_items)} folder items")
    session.add_all(folder_items)

    session.commit()


def downgrade():
    op.drop_index("idx_folder_items_item_type", table_name="folder_items")
    op.drop_index("idx_folder_items_item_id", table_name="folder_items")
    op.drop_index("idx_folder_items_folder_id", table_name="folder_items")
    op.drop_table("folder_items")
    op.drop_table("folders")
