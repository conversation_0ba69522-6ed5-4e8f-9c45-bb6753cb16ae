import { styled, t } from '@superset-ui/core';
import { Input } from 'src/components/Input';
import Icons from 'src/components/Icons';
import { BusinessDomain } from '../types';

interface DashboardSearchProps {
  currentBusiness?: BusinessDomain;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  setIsSearching: (isSearching: boolean) => void;
}

const SearchContainer = styled.div`
  padding: ${({ theme }) => theme.gridUnit * 4}px
    ${({ theme }) => theme.gridUnit * 6}px;
`;

const SearchTitle = styled.h2`
  margin: 0 0 ${({ theme }) => theme.gridUnit * 3}px 0;
  font-size: ${({ theme }) => theme.typography.sizes.m}px;
  color: ${({ theme }) => theme.colors.grayscale.dark1};
  font-weight: ${({ theme }) => theme.typography.weights.medium};
`;

const SearchInput = styled(Input)`
  .ant-input {
    border-radius: ${({ theme }) => theme.borderRadius}px;
    border: 1px solid ${({ theme }) => theme.colors.grayscale.light2};

    &:focus,
    &:hover {
      border-color: ${({ theme }) => theme.colors.primary.base};
    }
  }
`;

const DashboardSearch = ({
  currentBusiness,
  searchQuery,
  setSearchQuery,
  setIsSearching,
}: DashboardSearchProps) => (
  <SearchContainer>
    <SearchTitle>
      {t('Search Dashboards in %s', currentBusiness?.name.toUpperCase())}
    </SearchTitle>
    <SearchInput
      placeholder={t('Search')}
      value={searchQuery}
      onChange={e => setSearchQuery(e.target.value)}
      onFocus={() => setIsSearching(true)}
      onBlur={e => {
        if (!e.target.value) setIsSearching(false);
      }}
      prefix={<Icons.SearchOutlined iconSize="l" />}
      allowClear
    />
  </SearchContainer>
);

export default DashboardSearch;
