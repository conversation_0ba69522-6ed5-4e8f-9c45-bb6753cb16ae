import { useMemo, useCallback } from 'react';
import { useSelector } from 'react-redux';
import Loading from 'src/components/Loading';
import { useToasts } from 'src/components/MessageToasts/withToasts';
import { UserWithPermissionsAndRoles } from 'src/types/bootstrapTypes';
import { useFavoriteStatus } from 'src/views/CRUD/hooks';
import { Dashboard } from 'src/pages/DashboardList';
import { useFolder } from '../hooks/useFolder';
import { FOLDER_ADMIN_ROLE, FolderSlug, FolderType } from '../types';
import { DashboardFolders } from '../new_components';
import { createBusinessDomainFromFolders, extractDashboardIds } from '../utils';

interface IProps {
  permissions: {
    canEdit: boolean;
    canDelete: boolean;
    canExport: boolean;
  };
  actions: {
    onDeleteDashboard: (dashboard: Dashboard) => Promise<any>;
    onExportDashboard: (dashboard: Dashboard[]) => void;
    onEditDashboard: (dashboard: Dashboard) => void;
  };
}

const AdaptedFolderViewMode = ({ actions, permissions }: IProps) => {
  const { addDangerToast } = useToasts();
  const user = useSelector<any, UserWithPermissionsAndRoles>(
    state => state.user,
  );
  const canEditFolder = Boolean(
    user.roles?.Admin ||
      user.roles?.[FOLDER_ADMIN_ROLE[FolderType.Global]] ||
      user.roles?.[FOLDER_ADMIN_ROLE[FolderType.Plugin]],
  );

  const {
    result: globalFolder,
    status: globalStatus,
    refetch,
  } = useFolder(FolderSlug.Global);

  const dashboardIds = useMemo(
    () => extractDashboardIds(globalFolder),
    [globalFolder],
  );

  const [saveFavoriteStatus, favoriteStatus] = useFavoriteStatus(
    'dashboard',
    dashboardIds,
    addDangerToast,
  );

  const domainData = createBusinessDomainFromFolders(
    globalFolder,
    favoriteStatus,
  );

  const { canEdit, canDelete, canExport } = permissions;
  const { onDeleteDashboard, onExportDashboard, onEditDashboard } = actions;

  const handleDeleteDashboard = useCallback(
    async (dashboard: Dashboard) => {
      await onDeleteDashboard(dashboard);
      await refetch();
    },
    [onDeleteDashboard, refetch],
  );

  const handleEditDashboard = useCallback(
    (dashboard: Dashboard) => {
      onEditDashboard(dashboard);
    },
    [onEditDashboard],
  );

  const isLoading = globalStatus === 'loading';

  if (isLoading) {
    return <Loading />;
  }

  return (
    <DashboardFolders
      domainData={domainData}
      saveFavoriteStatus={saveFavoriteStatus}
      onDeleteDashboard={canDelete ? handleDeleteDashboard : undefined}
      onExportDashboard={canExport ? onExportDashboard : undefined}
      onEditDashboard={canEdit ? handleEditDashboard : undefined}
      canEditFolder={canEditFolder}
    />
  );
};

export default AdaptedFolderViewMode;
