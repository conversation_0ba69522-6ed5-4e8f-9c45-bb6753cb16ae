from typing import Any

from marshmallow import EXCLUDE, fields, post_dump, Schema, validate

from superset.tags.models import TagType

openapi_spec_methods_override = {
    "create": {"post": {"summary": "Create a new folder"}},
    "update": {"put": {"summary": "Update a folder"}},
    "delete": {"delete": {"summary": "Delete a folder"}},
    "get_by_slug": {"get": {"summary": "Get a folder with content by slug"}},
    "update_content": {"put": {"summary": "Update a folder content"}},
}


def post_dump_mapping(mapping: dict[str, str], data: dict[str, Any]) -> dict[str, Any]:
    for key, value in mapping.items():
        if key in data:
            data[value] = data.pop(key)
    return data


class FolderCreateSchema(Schema):
    name_ru = fields.String(required=True)
    name_en = fields.String(required=True)
    description_ru = fields.String(required=False, allow_none=True)
    description_en = fields.String(required=False, allow_none=True)
    type = fields.Integer(required=False, load_default=0)
    level = fields.Integer(required=False, load_default=2, validate=validate.Equal(2))
    parent_id = fields.Integer(required=True)
    order = fields.Integer(required=False, load_default=-1)


class FolderUpdateSchema(Schema):
    name_ru = fields.String(required=False, allow_none=True)
    name_en = fields.String(required=False, allow_none=True)
    description_ru = fields.String(required=False, allow_none=True)
    description_en = fields.String(required=False, allow_none=True)


class FolderContentItemSchema(Schema):
    type = fields.Integer(required=True)
    id = fields.Integer(required=True)
    order = fields.Integer(required=False, load_default=-1)

    item_id = fields.Integer(required=False, allow_none=True, dump_only=True)
    item_type = fields.Integer(required=False, allow_none=True, dump_only=True)

    class Meta:  # pylint: disable=too-few-public-methods
        unknown = EXCLUDE

    MAPPING = {
        "item_id": "id",
        "item_type": "type",
    }

    @post_dump
    def map_fields(self, data: dict[str, Any], **_: Any) -> dict[str, Any]:
        return post_dump_mapping(self.MAPPING, data)


class TagSchema(Schema):
    id = fields.Int()
    name = fields.String()
    type = fields.Enum(TagType, by_value=True)


class UserSchema(Schema):
    id = fields.Int()
    username = fields.String()
    first_name = fields.String()
    last_name = fields.String()
    email = fields.String()
    country_name = fields.String()


class FolderTreeSchema(Schema):
    # folder
    id = fields.Integer(required=False)
    name_ru = fields.String(required=False)
    name_en = fields.String(required=False)
    description_ru = fields.String(required=False)
    description_en = fields.String(required=False)
    folder_type = fields.Integer(required=False, attribute="type")
    parent_id = fields.Integer(required=False)
    folder_id = fields.Integer(required=False, dump_only=True)
    slug = fields.String(required=False, allow_none=True)
    level = fields.Integer(required=False)

    children = fields.Nested(lambda: FolderTreeSchema, many=True)

    # item
    item_id = fields.Integer(required=False, allow_none=True)
    item_type = fields.Integer(required=False, dump_default=0)

    # dashboard
    dashboard_title = fields.String(required=False, allow_none=True)
    dashboard_title_ru = fields.String(required=False, allow_none=True)
    certified_by = fields.String(required=False, allow_none=True)
    tags = fields.Nested(TagSchema, required=False, many=True)
    owners = fields.Nested(UserSchema, required=False, many=True)
    published = fields.Boolean(required=False, allow_none=True)
    changed_on_delta_humanized = fields.Function(
        lambda obj: obj.changed_on_delta_humanized(), required=False, allow_none=True
    )

    MAPPING = {
        "folder_id": "parent_id",
        "dashboard_title": "name_en",
        "dashboard_title_ru": "name_ru",
        "item_id": "id",
    }

    @post_dump
    def map_fields(self, data: dict[str, Any], **_: Any) -> dict[str, Any]:
        return post_dump_mapping(self.MAPPING, data)
